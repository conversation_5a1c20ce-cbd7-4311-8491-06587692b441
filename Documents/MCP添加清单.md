## Augment 目前在用的 MCP 服务

### 一个个加
```json
{
  "mcpServers": {
    "Exa Search": {
      "command": "npx",
      "args": [
        "-y",
        "exa-mcp-server"
      ],
      "env": {
        "EXA_API_KEY": "b226d29a-dc05-4977-bdda-5bc55d7328d4"
      }
    }
  }
}
{
  "mcpServers": {
    "firecrawl-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "firecrawl-mcp"
      ],
      "env": {
        "FIRECRAWL_API_KEY": "fc-a491e4cc8f3d4213808806c70a94449e"
      }
    }
  }
}
{
  "mcpServers": {
    "TikHub.io API Docs": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=4705614"
      ]
    }
  }
}
{
  "mcpServers": {
    "飞书 API - API 文档": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=532425"
      ]
    }
  }
}
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      }
    }
  }
}
{
  "mcpServers": {
    "Brave Search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAXComZb3HInv5jJ7htVVZ6aZczcli"
      }
    }
  }
}
{
  "mcpServers": {
    "promptx": {
      "command": "npx",
      "args": [
        "-y",
        "-f",
        "--registry",
        "https://registry.npmjs.org",
        "dpml-prompt@dev",
        "mcp-server"
      ]
    }
  }
}
{
  "mcpServers": {
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "mcp-deepwiki": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-deepwiki@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "tavily-remote-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-remote",
        "https://mcp.tavily.com/mcp/?tavilyApiKey=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"
      ]
    }
  }
}
{
  "mcpServers": {
    "markitdown-mcp": {
      "command": "/Users/<USER>/Downloads/Ming-Digital-Garden/venv/bin/markitdown-mcp",
      "args": []
    }
  }
}
{
  "mcpServers": {
    "Context 7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "Sequential thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}
{
  "mcpServers": {
    "Playwright": {
      "command": "npx",
      "args": [
        "-y",
        "@playwright/mcp@latest"
      ]
    }
  }
}
{
  "mcpServers": {
    "mcp-server-time": {
      "command": "uvx",
      "args": [
        "mcp-server-time",
        "--local-timezone=Asia/Shanghai"
      ]
    }
  }
}
```

### 集合

```json
{
  "mcpServers": {
    "Exa Search": {
      "command": "npx",
      "args": [
        "-y",
        "exa-mcp-server"
      ],
      "env": {
        "EXA_API_KEY": "b226d29a-dc05-4977-bdda-5bc55d7328d4"
      }
    },
    "firecrawl-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "firecrawl-mcp"
      ],
      "env": {
        "FIRECRAWL_API_KEY": "fc-a491e4cc8f3d4213808806c70a94449e"
      }
    },
    "TikHub.io API Docs": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=4705614"
      ]
    },
    "飞书 API - API 文档": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=532425"
      ]
    },
    "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      }
    },
    "Brave Search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAXComZb3HInv5jJ7htVVZ6aZczcli"
      }
    },
    "promptx": {
      "command": "npx",
      "args": [
        "-y",
        "-f",
        "--registry",
        "https://registry.npmjs.org",
        "dpml-prompt@dev",
        "mcp-server"
      ]
    },
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    },
    "mcp-deepwiki": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-deepwiki@latest"
      ]
    },
    "tavily-remote-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-remote",
        "https://mcp.tavily.com/mcp/?tavilyApiKey=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"
      ]
    },
    "markitdown-mcp": {
      "command": "/Users/<USER>/Downloads/Ming-Digital-Garden/venv/bin/markitdown-mcp",
      "args": []
    },
    "Context 7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    },
    "Sequential thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    },
    "Playwright": {
      "command": "npx",
      "args": [
        "-y",
        "@playwright/mcp@latest"
      ]
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-shrimp-task-manager"
      ],
      "env": {
        "DATA_DIR": "/Users/<USER>/Downloads/Ming-Digital-Garden/.shrimp",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "true",
        "PROJECT_AUTO_DETECT": "true"
      }
    },
    "mcp-server-time": {
      "command": "uvx",
      "args": [
        "mcp-server-time",
        "--local-timezone=Asia/Shanghai"
      ]
    }
  }
}
```

## Gemini cli



## 其他常用

```json
{
  "mcpServers": {
    "shrimp-task-manager": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-shrimp-task-manager"
      ],
      "env": {
        "DATA_DIR": "/Users/<USER>/Downloads/Ming-Digital-Garden/.shrimp",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "true",
        "PROJECT_AUTO_DETECT": "true"
      }
    }
  }
}
{
  "mcpServers": {
    "寸止": {
      "command": "寸止",
      "args": []
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": [
        "mcp-feedback-enhanced@latest"
      ]
    },
    "memory": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-memory"
      ],
      "env": {
        "MEMORY_FILE_PATH": "/Users/<USER>/Downloads/Ming-Digital-Garden/01-Memory/memory.json"
      }
    }
  }
}
{
  "mcpServers": {
    "@magicuidesign/mcp": {
      "command": "npx",
      "args": ["-y", "@magicuidesign/mcp@latest"]
    }
  }
}
{
  "mcpServers": {
    "codebase-mcp": {
      "command": "codebase-mcp",
      "env": {
        "GEMINI_API_KEY": "your-gemini-api-key-here"
      }
    },
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    }
  }
}
```
